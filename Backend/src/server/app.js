import express from 'express';
import cors from "cors"
import bodyParser from "body-parser"
import path from 'path';
const app = express();
app.use(bodyParser.json({ limit: '50mb' }));
app.use(bodyParser.urlencoded({ limit: '50mb', extended: true }));
app.use(cors(
    {
        origin:'*',
        Credential:true
    }
))
app.use(express.json({limit:"16kb"}))
app.use(express.urlencoded({extended:true}))
app.use(express.static("public/frontend"))
import {generateResponse} from './utils/commonResponse.js'

// ********Import the routes ******
import authRoutes from './routes/auth.routes.js'
import  fileRoutes from './routes/file.routes.js'
import mlJobRoutes from './routes/mlJob.routes.js'
import workflowRoute from './routes/workflow.routes.js'
import goldenValueRoute from './routes/goldenValue.routes.js'
import folderRoute from './routes/folder.routes.js'
import activityLog from './routes/activityLog.routes.js'
import  configurationsRoute from './routes/configurations.route.js'
import workflowFilters from './routes/workflowFilter.routes.js';
import kpiTrackingRoute from './routes/kpiTracking.routes.js';
import tasksRoute from './routes/tasks.route.js';
import chatRoutes from './routes/chat.routes.js'
import paraMeterMappingRoute from './routes/parameterMapping.route.js';
import commentRoute from './routes/comments.route.js';
import { addParameterMappings } from './controllers/parameterTypeMapping.controller.js';
import migrationRoutes from "./routes/migration.routes.js";
import sharedWorkflowRoutes from "./routes/workflowShared.routes.js";
import { insertSettingInRca, manipulateFilters, manipulateRCAFilters } from './controllers/workflow.controllers.js';
import sharedResultRoutes from "./routes/sharedResult.routes.js";
import bucketizationRoutes from "./routes/bucketization.routes.js"
import causalInferenceRoute from './routes/causalInference.routes.js';
import systemMetaRoutes from './routes/systemMeta.routes.js';
import multipartUploadRoutes from './routes/multipartUpload.routes.js';

// import viewRoutes from "./routes/view.routes.js"
import pannelRoutes from "./routes/pannel.routes.js"
//Add middleware here like
//app.use(isAuthenticated)   // It will authenticate first then go to route which is defined below

//Define the route
app.use('/api/v1/folder', folderRoute)
app.use('/api/v1/auth',authRoutes)
app.use('/api/v1/file',fileRoutes)
app.use('/api/v1/mljob',mlJobRoutes)
app.use('/api/v1/activityLog', activityLog)
app.use('/api/v1/update_results_workflow',mlJobRoutes)
app.use('/api/v1/workflow',workflowRoute)
app.use('/api/v1/golden-data', goldenValueRoute)
app.use('/api/v1/user',authRoutes)
app.use('/api/v1/configurations',configurationsRoute)
app.use('/api/v1/tasks',tasksRoute)
app.use('/api/v1/chat',chatRoutes)
app.use('/api/v1/filter', workflowFilters)
app.use('/api/v1/kpi-tracking', kpiTrackingRoute);
app.use('/api/v1/parameter-mapping', paraMeterMappingRoute);
app.use('/api/v1/comment', commentRoute);
app.use('/api/v1/migrate', migrationRoutes);
app.use('/api/v1/shared-workflow', sharedWorkflowRoutes);
app.use('/api/v1/shared-result', sharedResultRoutes);
app.use('/api/v1/bucketization', bucketizationRoutes);
app.use('/api/v1/causal-inference', causalInferenceRoute);
app.use('/api/v1/system-meta', systemMetaRoutes);
app.use('/api/v1/upload', multipartUploadRoutes);
// app.use('/api/v1/view', viewRoutes);
app.use('/api/v1/pannel', pannelRoutes);
app.use((req, res, next) => {

    // const fileExtensions = ['.js', '.css', '.png', '.jpg', '.jpeg', '.gif', '.svg'];
    // const isStaticFile = fileExtensions.some(ext => req.url.endsWith(ext));

    // if (isStaticFile) {
    //     console.log(`Serving static file: ${req.url}`);
    // }
    if (req.method === 'GET') return  res.sendFile(path.join(process.cwd(), 'public/frontend', 'index.html'));
    else return generateResponse(res, 404, 'Route not found')
});

// addParameterMappings().then(() => {
//     console.log("Parameter mappings added successfully.");
//   }).catch((err) => {
//     console.error("Error in adding parameter mappings:", err);
//   });


// manipulateFilters().then(() => {
//     console.log("Filters modified successfully.");
//   }).catch((err) => {
//     console.error("Error in modifying filters:", err);
//   });

// manipulateRCAFilters().then(() => {
//     console.log("Filters modified successfully.");
//   }).catch((err) => {
//     console.error("Error in modifying filters:", err);
//   });

// insertSettingInRca().then(() => {
//     console.log("rca modified successfully.");
//   }).catch((err) => {
//     console.error("Error in rca modify:", err);
//   });

// Error-handling middleware for other errors
app.use((err, req, res, next) => {
 console.log('err  Error-handling middleware for other errors:', err);
    // console.error(err.stack);
    return generateResponse(res, 500, 'Something went wrong!')
});
export { app };