import React, { useState } from "react";
import { Upload, message, Spin } from "antd";
import { InboxOutlined } from "@ant-design/icons";
import Select from "react-select";

const { Dragger } = Upload;

interface Option {
  value: string;
  label: string;
}

interface PLCExplorationConfig {
  system: string;
  batch_identifier: string;
  phase_identifier: string;
  x_feature: string;
  y_feature: string;
  all_features: string[];
}

interface PLCProps {
  systemName: string;
  plc: PLCExplorationConfig | null;
  onUpdate: (systemName: string, plc: PLCExplorationConfig) => void;
}

const PLCExplorationConfig: React.FC<PLCProps> = ({ systemName, plc, onUpdate }) => {
  const [csvColumns, setCsvColumns] = useState<string[]>([]);
  const [isFileUploaded, setIsFileUploaded] = useState(false);
  const [isExtractingColumns, setIsExtractingColumns] = useState(false);
  const [uploadedFileName, setUploadedFileName] = useState<string>("");
  const [mappingData, setMappingData] = useState({
    batch_identifier: "",
    x_feature: "DateTime", // Default to DateTime
    y_feature: "",
  });

  const handleFileUpload = (file: File) => {
    setIsExtractingColumns(true);
    setUploadedFileName(file.name);
    
    const reader = new FileReader();
    reader.onload = (e) => {
      const text = e.target?.result as string;
      const lines = text.split('\n');
      if (lines.length > 0) {
        const headers = lines[0].split(',').map(header => header.trim().replace(/"/g, ''));
        setCsvColumns(headers);
        setIsFileUploaded(true);
        message.success('CSV file uploaded successfully!');
      }
      setIsExtractingColumns(false);
    };
    reader.onerror = () => {
      message.error('Failed to read CSV file');
      setIsExtractingColumns(false);
    };
    reader.readAsText(file);
    return false; // Prevent default upload behavior
  };

  const handleMappingChange = (field: string, value: string) => {
    setMappingData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSaveMapping = () => {
    if (!mappingData.batch_identifier || !mappingData.y_feature) {
      message.error('Please map all required fields before saving');
      return;
    }

    const plcConfig: PLCExplorationConfig = {
      system: systemName,
      batch_identifier: mappingData.batch_identifier,
      phase_identifier: "Phase", // Fixed value
      x_feature: mappingData.x_feature,
      y_feature: mappingData.y_feature,
      all_features: csvColumns
    };

    onUpdate(systemName, plcConfig);
    message.success('PLC Exploration Configuration saved!');
  };

  const uploadProps = {
    name: 'file',
    multiple: false,
    accept: '.csv',
    beforeUpload: handleFileUpload,
    showUploadList: false,
  };

  const columnOptions: Option[] = csvColumns.map(col => ({
    value: col,
    label: col
  }));

  return (
    <div className="space-y-8">
      <div className="flex items-center space-x-2 mb-6">
        <span className="bg-blue-600 text-white px-2 py-1 rounded-full text-sm">
          Step 8/8
        </span>
        <h2 className="text-xl font-bold">PLC Exploration Configuration</h2>
      </div>

      {/* Step 1: CSV Upload */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold mb-4">Step 1: Upload CSV File</h3>
        <style>
          {`
            .plc-upload-area .ant-upload.ant-upload-drag {
              border: 2px dashed #bfbfbf !important;
              border-radius: 8px !important;
              background: #fafbfc !important;
              min-height: 120px;
              box-sizing: border-box;
            }
            .plc-upload-area .ant-upload.ant-upload-drag:hover,
            .plc-upload-area .ant-upload.ant-upload-drag-active {
              border-color: #1890ff !important;
              background: #f0f5ff !important;
            }
          `}
        </style>
        {/* Show uploaded file name if file is uploaded */}
        {isFileUploaded && uploadedFileName && (
          <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <span className="text-green-600">✓</span>
              <span className="text-sm font-medium text-green-800">
                File uploaded: {uploadedFileName}
              </span>
            </div>
          </div>
        )}
        <div className="plc-upload-area">
          <Spin spinning={isExtractingColumns} tip="Extracting columns from CSV...">
            <Dragger {...uploadProps}>
              <p className="ant-upload-drag-icon">
                <InboxOutlined />
              </p>
              <p className="ant-upload-text">Click or drag CSV file to this area to upload</p>
              <p className="ant-upload-hint">
                Support for a single CSV file. The file will be used to extract column headers.
              </p>
            </Dragger>
          </Spin>
        </div>
      </div>

      {/* Step 2: Column Mapping */}
      {isFileUploaded && (
        <div className="mb-8">
          <h3 className="text-lg font-semibold mb-4">Step 2: Map Columns</h3>
          <div className="bg-gray-50 p-6 rounded-lg">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Batch Identifier *
                </label>
                <Select
                  options={columnOptions}
                  value={columnOptions.find(opt => opt.value === mappingData.batch_identifier)}
                  onChange={(selected) => handleMappingChange('batch_identifier', selected?.value || '')}
                  placeholder="Select batch identifier column"
                  className="basic-single-select"
                  classNamePrefix="select"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  X Feature (DateTime)
                </label>
                <Select
                  options={columnOptions}
                  value={columnOptions.find(opt => opt.value === mappingData.x_feature)}
                  onChange={(selected) => handleMappingChange('x_feature', selected?.value || '')}
                  placeholder="Select DateTime column"
                  className="basic-single-select"
                  classNamePrefix="select"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Y Feature *
                </label>
                <Select
                  options={columnOptions}
                  value={columnOptions.find(opt => opt.value === mappingData.y_feature)}
                  onChange={(selected) => handleMappingChange('y_feature', selected?.value || '')}
                  placeholder="Select Y feature column"
                  className="basic-single-select"
                  classNamePrefix="select"
                />
              </div>
            </div>

            <div className="mt-6">
              <h4 className="text-md font-medium mb-3">Available Columns ({csvColumns.length})</h4>
              <div className="bg-white p-4 rounded border max-h-40 overflow-y-auto">
                <div className="flex flex-wrap gap-2">
                  {csvColumns.map((column, index) => (
                    <span
                      key={index}
                      className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm"
                    >
                      {column}
                    </span>
                  ))}
                </div>
              </div>
              <p className="text-sm text-gray-500 mt-2">
                All columns will be included in the configuration
              </p>
            </div>

            <div className="mt-6">
              <button
                onClick={handleSaveMapping}
                className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
              >
                Save Mapping
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Current Configuration Display */}
      {plc && (
        <div className="mb-8">
          <h3 className="text-lg font-semibold mb-4">Current Configuration</h3>
          <div className="bg-gray-50 p-4 rounded-lg">
            <pre className="text-sm overflow-x-auto">
              {JSON.stringify(plc, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
};

export default PLCExplorationConfig; 