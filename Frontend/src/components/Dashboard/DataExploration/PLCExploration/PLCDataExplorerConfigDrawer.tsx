import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import {
  Drawer,
  Form,
  Button,
  DatePicker,
  Select,
  Typography,
  message,
  Spin
} from 'antd';
import { CloseOutlined, SettingOutlined } from '@ant-design/icons';
import { postRequest } from '../../../../utils/apiHandler';
import dayjs from 'dayjs';
import { QueryBuilder as ReactQueryBuilder, formatQuery } from "react-querybuilder";

const { Title } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

interface PLCDataExplorerConfigDrawerProps {
  open: boolean;
  onClose: () => void;
  onDataLoad: (data: any) => void;
  onFiltersChange?: (filters: any) => void;
}

const PLCDataExplorerConfigDrawer: React.FC<PLCDataExplorerConfigDrawerProps> = ({
  open,
  onClose,
  onDataLoad,
  onFiltersChange
}) => {
  const [form] = Form.useForm();
  const [batchMetaLoading, setBatchMetaLoading] = useState(false);

  // Master loading state
  const isMasterLoading = batchMetaLoading;
  
  // Get current loading message based on active operation
  const getLoadingMessage = () => {
    if (batchMetaLoading) return 'Loading explorer data...';
    return 'Processing...';
  };
  
  // Get selected systems from Redux state
  const selectSystems = useSelector((state: any) => state.systems.systems);
  
  // Helper function to extract system names from selected systems
  const getSystemNames = () => {
    if (!selectSystems || !selectSystems.length || !selectSystems[0]?.systems) {
      return [];
    }
    
    return selectSystems[0].systems.map((system: any) => ({
      systemId: system.systemId,
      systemName: system.systemName
    }));
  };

  // Batch management state
  const [availableBatches, setAvailableBatches] = useState<string[]>([]);
  const [qualityBatchIds, setQualityBatchIds] = useState<Array<{
    batch_id: string;
    batch_quality: number;
    is_batch_good: boolean;
  }>>([]);
  const [selectedBatches, setSelectedBatches] = useState<string[]>([]);
  const [compareLimit, setCompareLimit] = useState<number>(5);

  // Recent searches management
  const [recentSearches, setRecentSearches] = useState<Array<{label: string, value: [any, any]}>>([]);
  const RECENT_SEARCHES_KEY = 'plc_explorer_recent_searches';
  const MAX_RECENT_SEARCHES = 5;

  // Query Builder state management
  const [showQueryBuilder, setShowQueryBuilder] = useState(true);
  const [filterQuery, setFilterQuery] = useState({ combinator: "and", rules: [] });
  const [appliedFilters, setAppliedFilters] = useState<any>(null);
  const [currentDateRange, setCurrentDateRange] = useState<[any, any] | null>(null);

  // Get available columns from all_features in selected systems' PLC configurations
  const getAvailableColumns = () => {
    const systemFeatures: string[] = [];
    
    // Extract all_features from selected systems' PLC configurations
    if (selectSystems && selectSystems.length > 0 && selectSystems[0]?.config) {
      // Iterate through the config array to find PLC configurations
      selectSystems[0].config.forEach((systemConfig: any) => {
        if (systemConfig && systemConfig.PLC && systemConfig.PLC.all_features) {
          systemFeatures.push(...systemConfig.PLC.all_features);
        }
      });
    }
    
    // Return unique features from system configurations
    const uniqueFeatures = Array.from(new Set(systemFeatures));
    return uniqueFeatures;
  };

  // Define fields for the new query builder
  const queryBuilderFields = getAvailableColumns().map(field => ({ name: field, label: field }));

  // Handle query builder changes with detailed logging
  const handleQueryChange = (newQuery: any) => {
    setFilterQuery(newQuery);
    setAppliedFilters(newQuery);
    if (onFiltersChange) onFiltersChange(newQuery);

    if (newQuery.rules && newQuery.rules.length > 0) {
      console.log('Individual Rules:');
      newQuery.rules.forEach((rule: any, index: number) => {
        console.log(`  Rule ${index + 1}:`, {
          field: rule.field,
          operator: rule.operator,
          value: rule.value
        });
      });
    }
  };

  // Load recent searches from localStorage
  useEffect(() => {
    const saved = localStorage.getItem(RECENT_SEARCHES_KEY);
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        const validSearches = parsed.map((search: any) => ({
          label: search.label,
          value: [dayjs(search.value[0]), dayjs(search.value[1])] as [any, any]
        }));
        setRecentSearches(validSearches);
      } catch (error) {
        console.error('Error loading recent searches:', error);
      }
    }
  }, []);

  // Save a new date range to recent searches
  const saveToRecentSearches = (startDateTime: any, endDateTime: any) => {
    const newSearch = {
      label: `${startDateTime.format('MMM DD')} - ${endDateTime.format('MMM DD, YYYY')}`,
      value: [startDateTime, endDateTime] as [any, any]
    };

    const existing = recentSearches.filter(search => 
      search.label !== newSearch.label
    );
    
    const updated = [newSearch, ...existing].slice(0, MAX_RECENT_SEARCHES);
    setRecentSearches(updated);

    // Save to localStorage
    const toSave = updated.map(search => ({
      label: search.label,
      value: [search.value[0].toISOString(), search.value[1].toISOString()]
    }));
    
    localStorage.setItem(RECENT_SEARCHES_KEY, JSON.stringify(toSave));
  };

  // Create presets for RangePicker
  const createDatePresets = () => {
    const commonPresets = [
      {
        label: 'Last 24 hours',
        value: () => [dayjs().subtract(1, 'day'), dayjs()] as [any, any]
      },
      {
        label: 'Last 7 days', 
        value: () => [dayjs().subtract(7, 'days'), dayjs()] as [any, any]
      },
      {
        label: 'Last 30 days',
        value: () => [dayjs().subtract(30, 'days'), dayjs()] as [any, any]
      },
      {
        label: 'This month',
        value: () => [dayjs().startOf('month'), dayjs()] as [any, any]
      }
    ];

    const allPresets = [...commonPresets];
    
    if (recentSearches.length > 0) {
      const recentPresets = recentSearches.map(search => ({
        label: `📅 ${search.label}`,
        value: () => search.value as [any, any]
      }));
      
      allPresets.push(...recentPresets);
    }

    return allPresets;
  };

  // Function to fetch batch metadata and chart data
  const fetchBatchMetadata = async (startDateTime: any, endDateTime: any, filters?: any) => {
    setBatchMetaLoading(true);
    try {
      const systems = getSystemNames();
      const systemNames = systems.map((s: any) => s.systemName).join(' + ') || '';

      const apiPayload = {
        start_datetime: startDateTime.format('YYYY-MM-DD HH:mm'),
        end_datetime: endDateTime.format('YYYY-MM-DD HH:mm'),
        system: systemNames,
        batch_feature: 'batch_id',
        x_feature: "",
        y_feature: "",
        ...(filters && { filters: formatQuery(filters, 'sql') })
      };

      const response = await postRequest('/file/explore-plc/get-batch-meta', apiPayload);

      if (response.data.data) {
        const apiData = response.data.data;
        
        // Extract batch metadata (for backward compatibility)
        const { batch_ids, quality_batch_ids, compare_limit, columnOptions } = apiData;
        
        if (quality_batch_ids && Array.isArray(quality_batch_ids)) {
          setQualityBatchIds(quality_batch_ids);
          setAvailableBatches(quality_batch_ids.map(batch => batch.batch_id));
        } else {
          setAvailableBatches(batch_ids || []);
          setQualityBatchIds([]);
        }
        
        setCompareLimit(compare_limit || 5);
        setSelectedBatches(batch_ids || []);

        // Get available columns from API response
        const availableColumns = columnOptions ? Object.keys(columnOptions) : [];

        // Create explorer data with chart data from get-batch-meta
        const explorerData = {
          ...apiData,
          selectedBatches: batch_ids || [],
          availableBatches: batch_ids || [],
          qualityBatchIds: quality_batch_ids || [],
          compareLimit: compare_limit || 5,
          columns: availableColumns,
          dateRange: {
            startDateTime: startDateTime,
            endDateTime: endDateTime
          }
        };

        // Save successful search to recent searches
        saveToRecentSearches(startDateTime, endDateTime);
        setShowQueryBuilder(true);

        // Pass data directly to parent component
        onDataLoad(explorerData);
        
        message.success('Explorer data loaded successfully');

        // onClose();
        
        return true;
      } else {
        message.error('Failed to fetch explorer data');
        setAvailableBatches([]);
        setQualityBatchIds([]);
        setCompareLimit(5);
        return false;
      }
    } catch (error) {
      console.error('Error fetching explorer data:', error);
      message.error('Error fetching explorer data');
      setAvailableBatches([]);
      setQualityBatchIds([]);
      setCompareLimit(5);
      return false;
    } finally {
      setBatchMetaLoading(false);
    }
  };



  // Handle form submission (date range submission)
  const handleSubmit = async (values: any) => {
    if (!values.dateTimeRange || !values.dateTimeRange[0] || !values.dateTimeRange[1]) {
      message.error('Please select date and time range before submitting');
      return;
    }

    const [startDateTime, endDateTime] = values.dateTimeRange;

    if (startDateTime.isAfter(endDateTime)) {
      message.error('Start date/time cannot be after end date/time');
      return;
    }

    setCurrentDateRange([startDateTime, endDateTime]);

    const success = await fetchBatchMetadata(startDateTime, endDateTime);
    if (!success) {
      return;
    }
  };

  // Handle applying filters and refetching data
  const handleApplyFilters = async () => {
    if (!currentDateRange) {
      message.error('No date range available. Please load data first.');
      return;
    }

    if (!appliedFilters || !appliedFilters.rules || appliedFilters.rules.length === 0) {
      message.warning('No filters to apply. Please add filters or use "Clear Filters" instead.');
      return;
    }

    const [startDateTime, endDateTime] = currentDateRange;
    const success = await fetchBatchMetadata(startDateTime, endDateTime, appliedFilters);

    if (success) {
      if (onFiltersChange) onFiltersChange(appliedFilters);
      handleClose();
    }
  };

  // Handle drawer close
  const handleClose = () => {
    // Reset query builder state when closing
    // setShowQueryBuilder(false);
    // setFilterQuery({ combinator: "and", rules: [] });
    // setAppliedFilters(null);
    // setCurrentDateRange(null);

    onClose();
  };

  // Render enhanced batch option with quality information
  const renderBatchOption = (batchInfo: { batch_id: string; batch_quality: number; is_batch_good: boolean }) => {
    const { batch_id, batch_quality, is_batch_good } = batchInfo;
    const qualityColor = is_batch_good ? '#52c41a' : '#ff4d4f';
    const qualityBg = is_batch_good ? '#f6ffed' : '#fff2f0';
    
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: '4px 8px',
        backgroundColor: qualityBg,
        borderRadius: '4px',
        border: `1px solid ${qualityColor}20`
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '6px'
        }}>
          <span style={{ 
            fontWeight: 500,
            color: '#333'
          }}>
            {batch_id}
          </span>
        </div>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '4px'
        }}>
          <span style={{
            fontSize: '11px',
            color: qualityColor,
            fontWeight: 600,
            backgroundColor: qualityColor + '15',
            padding: '2px 6px',
            borderRadius: '10px'
          }}>
            {batch_quality.toFixed(2)}
          </span>
          <span style={{
            fontSize: '14px'
          }}>
            {is_batch_good ? '✅' : '❌'}
          </span>
        </div>
      </div>
    );
  };

  return (
    <Drawer
      title={
        <div className="flex items-center justify-between">
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            fontSize: '16px',
            fontWeight: 600
          }}>
            <SettingOutlined style={{ color: '#1890ff' }} />
            Explorer Configuration
          </div>
          <Button
            type="text"
            icon={<CloseOutlined />}
            onClick={handleClose}
            size="small"
            disabled={isMasterLoading}
          />
        </div>
      }
      placement="right"
      width={615}
      open={open}
      onClose={handleClose}
      closable={false}
      maskClosable={!isMasterLoading}
      styles={{
        body: { padding: '0', position: 'relative' }
      }}
    >
      {/* Full Screen Loading Overlay */}
      <Spin 
        spinning={isMasterLoading} 
        tip={getLoadingMessage()}
        size="large"
        style={{
          minHeight: '100vh'
        }}
      >
        <div 
          className="plc-explorer-config-drawer" 
          style={{ 
            minHeight: '100vh',
            pointerEvents: isMasterLoading ? 'none' : 'auto'
          }}
          onKeyDown={(e) => {
            if (isMasterLoading) {
              e.preventDefault();
              e.stopPropagation();
            }
          }}
        >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          className="plc-config-form"
        >
          {/* Data Selection Section */}
          <div style={{
            background: '#ffffff',
            borderRadius: '12px',
            padding: '20px',
            margin: '16px',
            marginBottom: '20px',
            border: '1px solid #e8e8e8',
            boxShadow: '0 2px 8px rgba(0,0,0,0.06)'
          }}>
            {/* Date and Time Range Selection */}
            <Form.Item
              label={<span style={{ fontSize: '13px', fontWeight: 500, color: '#666' }}>Date & Time Range</span>}
              name="dateTimeRange"
              style={{ marginBottom: '16px' }}
              rules={[
                { required: true, message: 'Please select date and time range!' }
              ]}
            >
              <RangePicker
                showTime={{ format: 'HH:mm:ss' }}
                format="YYYY-MM-DD HH:mm:ss"
                placeholder={['Start Date & Time', 'End Date & Time']}
                disabled={isMasterLoading}
                style={{ width: '100%', borderRadius: '8px' }}
                size="middle"
                presets={createDatePresets()}
                onCalendarChange={(dates) => {
                  if (dates && dates.length === 2) {
                    const [start, end] = dates;
                    if (start && end) {
                      setCurrentDateRange([start, end]);
                    } else {
                      setCurrentDateRange(null);
                    }
                  } else {
                    setCurrentDateRange(null);
                  }
                }}
              />
            </Form.Item>

            {/* Submit Button in Data Selection Zone */}
            <div style={{ marginTop: '20px' }}>
              <Button
                type="primary"
                htmlType="submit"
                loading={batchMetaLoading}
                disabled={isMasterLoading}
                style={{
                  width: '100%',
                  height: '40px',
                  fontSize: '14px',
                  fontWeight: 600,
                  borderRadius: '8px',
                  background: '#1890ff',
                  border: 'none'
                }}
                size="large"
              >
                {batchMetaLoading ? 'Loading Explorer Data...' : 'Load Explorer Data'}
              </Button>
            </div>

          </div>

          {/* New Query Builder Section - appears after Load Explorer Data button click */}
          {showQueryBuilder && (
            <div style={{
              background: '#ffffff',
              borderRadius: '12px',
              padding: '20px',
              margin: '16px',
              marginTop: '20px',
              border: '1px solid #e8e8e8',
              boxShadow: '0 2px 8px rgba(0,0,0,0.06)'
            }}>
              <div style={{
                marginBottom: '16px',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}>
                <span style={{
                  fontSize: '14px',
                  fontWeight: 600,
                  color: '#333'
                }}>
                  Data Filters
                </span>
                {/* <Button
                  type="text"
                  size="small"
                  onClick={() => {
                    // setShowQueryBuilder(false);
                    setFilterQuery({ combinator: "and", rules: [] });
                    setAppliedFilters(null);
                  }}
                  style={{ color: '#666' }}
                >
                  Hide Filters
                </Button> */}
              </div>

              <div style={{
                border: '1px solid #d9d9d9',
                borderRadius: '8px',
                padding: '16px',
                backgroundColor: '#fafafa'
              }}>
                <ReactQueryBuilder
                  fields={queryBuilderFields}
                  query={filterQuery}
                  onQueryChange={handleQueryChange}
                />
              </div>

              {appliedFilters && appliedFilters.rules && appliedFilters.rules.length > 0 && (
                <div style={{
                  marginTop: '12px',
                  padding: '8px 12px',
                  backgroundColor: '#e6f7ff',
                  border: '1px solid #91d5ff',
                  borderRadius: '6px',
                  fontSize: '12px',
                  color: '#0050b3'
                }}>
                  <strong>Active Filters:</strong> {appliedFilters.rules.length} filter(s) applied
                </div>
              )}

              <div style={{ marginTop: '16px', display: 'flex', gap: '8px', justifyContent: 'space-between' }}>
                <Button
                  onClick={() => {
                    setFilterQuery({ combinator: "and", rules: [] });
                    setAppliedFilters(null);
                    if (onFiltersChange) onFiltersChange(null);
                  }}
                  disabled={!appliedFilters || !appliedFilters.rules || appliedFilters.rules.length === 0}
                >
                  Clear Filters
                </Button>

                <div style={{ display: 'flex', gap: '8px' }}>
                  {/* <Button
                    onClick={handleClose}
                    disabled={batchMetaLoading}
                  >
                    Close Without Applying
                  </Button> */}
                  <Button
                    type="primary"
                    onClick={handleApplyFilters}
                    loading={batchMetaLoading}
                    disabled={!appliedFilters || !appliedFilters.rules || appliedFilters.rules.length === 0}
                  >
                    {batchMetaLoading ? 'Applying Filters...' : 'Apply Filters & Reload Data'}
                  </Button>
                </div>
              </div>
            </div>
          )}
        </Form>
        </div>
      </Spin>
    </Drawer>
  );
};

export default PLCDataExplorerConfigDrawer;