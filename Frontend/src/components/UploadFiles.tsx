import React, { useState, useRef, useEffect } from 'react';
import { Upload } from 'antd';
import {   FileOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseOutlined, } from '@ant-design/icons';
import { getRequest, postRequest } from '../utils/apiHandler';


interface UploadState {
  file: File | null;
  progress: number;
  speed: number;
  status: 'idle' | 'uploading' | 'completed' | 'error' | 'bucket error';
  error: string | null;
  uploadId: string | null;
  key: string | null;
}

const FileUploader: React.FC = () => {
  const [uploadState, setUploadState] = useState<UploadState>({
    file: null,
    progress: 0,
    speed: 0,
    status: 'idle',
    error: null,
    uploadId: null,
    key: null,
  });

  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  const [path, setPath] = useState('data/input/plc');
  const [dropdownValue, setDropdownValue] = useState('plc');

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatSpeed = (bytesPerSecond: number): string => {
    return formatFileSize(bytesPerSecond) + '/s';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleFileSelect = (file: File) => {
    if (file.size > 2 * 1024 * 1024 * 1024) { // 2GB limit
      setUploadState(prev => ({
        ...prev,
        error: 'File size exceeds 2GB limit',
        status: 'error',
      }));
      return;
    }

    setUploadState(prev => ({
      ...prev,
      file,
      progress: 0,
      speed: 0,
      status: 'idle',
      error: null,
    }));
  };

  const uploadFile = async () => {
    if (!uploadState.file) return;

    if (!path) {
      setUploadState(prev => ({
        ...prev,
        error: 'Path is required',
        status: 'bucket error'
      }));
      return;
    }

    setUploadState(prev => ({
      ...prev,
      status: 'uploading',
      progress: 0,
      speed: 0,
      error: null,
    }));

    const abortController = new AbortController();
    abortControllerRef.current = abortController;

    try {
      // Step 1: Initiate multipart upload
      const initiateResponse = await postRequest('/upload/initiate', {
        path,
        fileName: uploadState.file.name,
        fileType: uploadState.file.type,
        fileSize: uploadState.file.size,
      });

      if (initiateResponse.status !== 200) throw new Error('Failed to initiate upload');

      const { uploadId, key, totalParts, chunkSize } = initiateResponse.data.data;
      
      setUploadState(prev => ({ ...prev, uploadId, key }));

      // Step 2: Upload parts in parallel
      const uploadedParts: Array<{ ETag: string; PartNumber: number }> = [];
      const startTime = Date.now();
      let uploadedBytes = 0;

      // Get presigned URLs for all parts
      const partNumbers = Array.from({ length: totalParts }, (_, i) => i + 1);
      const urlsResponse = await postRequest('/upload/parts', {
        path,
        uploadId,
        key,
        partNumbers
      });

      if (urlsResponse.status !== 200) throw new Error('Failed to get presigned URLs');

      const { presignedUrls } = urlsResponse.data.data;

      // Upload parts with controlled concurrency (4 parallel uploads)
      const maxConcurrency = 4;
      const uploadPromises: Promise<void>[] = [];
      
      for (let i = 0; i < presignedUrls.length; i += maxConcurrency) {
        const batch = presignedUrls.slice(i, i + maxConcurrency);
        
        const batchPromises = batch.map(async ({ partNumber, presignedUrl }: any) => {
          const start = (partNumber - 1) * chunkSize;
          const end = Math.min(start + chunkSize, uploadState.file!.size);
          const chunk = uploadState.file!.slice(start, end);

          const uploadResponse = await fetch(presignedUrl, {
            method: 'PUT',
            body: chunk,
            signal: abortController.signal,
          });

          if (!uploadResponse.ok) throw new Error(`Failed to upload part ${partNumber}`);

          const etag = uploadResponse.headers.get('ETag');
          if (!etag) throw new Error(`No ETag received for part ${partNumber}`);

          uploadedParts.push({ ETag: etag, PartNumber: partNumber });
          uploadedBytes += chunk.size;

          // Update progress and speed
          const elapsedTime = (Date.now() - startTime) / 1000;
          const speed = uploadedBytes / elapsedTime;
          const progress = (uploadedBytes / uploadState.file!.size) * 100;

          setUploadState(prev => ({
            ...prev,
            progress,
            speed,
          }));
        });

        uploadPromises.push(...batchPromises);
        await Promise.all(batchPromises);
      }

      // Step 3: Complete multipart upload
      const completeResponse = await postRequest('/upload/complete', {
        path,
        uploadId,
        key,
        parts: uploadedParts,
      });

      if (completeResponse.status !== 200) throw new Error('Failed to complete upload');

      setUploadState(prev => ({
        ...prev,
        status: 'completed',
        progress: 100,
      }));

    } catch (error: any) {
      console.log('errorrrrrrrrrrr', error)
      if (error.name === 'AbortError') {
        // Handle abort
        if (uploadState.uploadId && uploadState.key) {
          try {
            await postRequest('/upload/abort', {
              path,
              uploadId: uploadState.uploadId,
              key: uploadState.key,
            });
          } catch (abortError) {
            console.error('Failed to abort upload:', abortError);
          }
        }
        
        setUploadState(prev => ({
          ...prev,
          status: 'idle',
          progress: 0,
          speed: 0,
        }));
      } else {
        setUploadState(prev => ({
          ...prev,
          status: 'error',
          error: error.message || 'Upload failed',
        }));
      }
    } finally {
      abortControllerRef.current = null;
    }
  };

  const cancelUpload = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  };

  const resetUpload = () => {
    setUploadState({
      file: null,
      progress: 0,
      speed: 0,
      status: 'idle',
      error: null,
      uploadId: null,
      key: null,
    });
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100 px-4">
      <div className="max-w-2xl w-full">
        <div
        className={`
          relative border-2 border-dashed rounded-xl p-8 text-center transition-all duration-200 shadow-lg bg-white
          ${isDragOver 
            ? 'border-blue-500 bg-blue-50' 
            : 'border-gray-300 hover:border-gray-400'
          }
          ${uploadState.status === 'uploading' ? 'pointer-events-none opacity-75' : ''}
        `}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          onChange={handleFileInputChange}
          className="hidden"
          accept="*/*"
        />

          {!uploadState.file ? (
            <div className="space-y-4">
              <Upload className="w-16 h-16 text-gray-400 mx-auto" />
              <div>
                <h3 className="text-xl font-semibold text-gray-700 mb-2">
                  Drop your file here or click to browse
                </h3>
                <p className="text-gray-500 mb-4">Supports large files</p>
                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors duration-200 font-medium shadow-sm"
                >
                  Select File
                </button>
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              {/* File Info */}
              <div className="flex items-center space-x-4 bg-gray-50 p-4 rounded-lg shadow-sm">
                <FileOutlined className="w-8 h-8 text-blue-600 flex-shrink-0" />
                <div className="flex-1 text-left">
                  <h4 className="font-semibold text-gray-800 truncate">
                    {uploadState.file.name}
                  </h4>
                  <p className="text-sm text-gray-500">
                    {formatFileSize(uploadState.file.size)}
                  </p>
                </div>
                <button
                  onClick={resetUpload}
                  disabled={uploadState.status === 'uploading'}
                  className="text-gray-400 hover:text-gray-600 transition-colors duration-200 disabled:opacity-50"
                >
                  <CloseOutlined className="w-5 h-5" />
                </button>
              </div>

              {/* Progress Bar */}
              {uploadState.status === 'uploading' && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Uploading...</span>
                    <span className="text-gray-600">
                      {Math.round(uploadState.progress)}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
                    <div
                      className="bg-blue-600 h-3 rounded-full transition-all duration-300 ease-out"
                      style={{ width: `${uploadState.progress}%` }}
                    />
                  </div>
                  <div className="flex justify-between text-sm text-gray-500">
                    <span>Speed: {formatSpeed(uploadState.speed)}</span>
                    <span>
                      ETA: {uploadState.speed > 0 
                        ? Math.round((uploadState.file.size * (1 - uploadState.progress / 100)) / uploadState.speed) + 's'
                        : '--'
                      }
                    </span>
                  </div>
                </div>
              )}

              {/* Status Messages */}
              {uploadState.status === 'completed' && (
                <div className="flex items-center space-x-2 text-green-600 bg-green-50 p-3 rounded-lg">
                  <CheckCircleOutlined className="w-5 h-5" />
                  <span className="font-medium">Upload completed successfully!</span>
                </div>
              )}

              {uploadState.status === 'error' && (
                <div className="flex items-center space-x-2 text-red-600 bg-red-50 p-3 rounded-lg">
                  <ExclamationCircleOutlined className="w-5 h-5" />
                  <span className="font-medium">{uploadState.error}</span>
                </div>
              )}

              {uploadState.status === 'bucket error' && (
                <div className="flex items-center space-x-2 text-red-600 bg-red-50 p-3 rounded-lg">
                  <ExclamationCircleOutlined className="w-5 h-5" />
                  <span className="font-medium">{uploadState.error}</span>
                </div>
              )}

              {uploadState.file && (
                <div className="space-y-2">
                  <select
                    value={dropdownValue}
                    onChange={(e) => {
                      const value = e.target.value;
                      setDropdownValue(value);

                      if (value === "plc") {
                        setPath("data/input/plc");
                      } else if (value === "quality") {
                        setPath("data/input/quality");
                      } else {
                        setPath("");
                      }
                    }}                    
                    className="border rounded px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {/* <option value="">Select an option</option> */}
                    <option value="plc">PLC</option>
                    <option value="quality">Quality</option>
                    <option value="other">Other</option>
                  </select>

                  {dropdownValue == 'other' && (
                    <input
                      type="text"
                      placeholder="Enter Path"
                      value={path}
                      onChange={(e) => setPath(e.target.value)}
                      className="border rounded px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  )}
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex space-x-4">
                {(uploadState.status === 'idle' || uploadState.status === 'bucket error') && (
                  <button
                    onClick={uploadFile}
                    className="flex-1 bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 transition-colors duration-200 font-medium shadow-sm"
                  >
                    Upload File
                  </button>
                )}

                {uploadState.status === 'uploading' && (
                  <button
                    onClick={cancelUpload}
                    className="flex-1 bg-red-600 text-white py-3 rounded-lg hover:bg-red-700 transition-colors duration-200 font-medium shadow-sm"
                  >
                    Cancel Upload
                  </button>
                )}

                {(uploadState.status === 'completed' || uploadState.status === 'error') && (
                  <button
                    onClick={resetUpload}
                    className="flex-1 bg-gray-600 text-white py-3 rounded-lg hover:bg-gray-700 transition-colors duration-200 font-medium shadow-sm"
                  >
                    Upload Another File
                  </button>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FileUploader;